import request from '@/utils/request'

// 查询会议室信息查询列表
export function listRoom(query) {
  return request({
    url: '/search/room/list',
    method: 'get',
    params: query
  })
}

// 查询会议室信息查询详细
export function getRoom(roomId) {
  return request({
    url: '/search/room/' + roomId,
    method: 'get'
  })
}

// 新增会议室信息查询
export function addRoom(data) {
  return request({
    url: '/search/room',
    method: 'post',
    data: data
  })
}

// 修改会议室信息查询
export function updateRoom(data) {
  return request({
    url: '/search/room',
    method: 'put',
    data: data
  })
}

// 删除会议室信息查询
export function delRoom(roomId) {
  return request({
    url: '/search/room/' + roomId,
    method: 'delete'
  })
}

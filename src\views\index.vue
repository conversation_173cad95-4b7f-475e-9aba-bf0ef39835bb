<template>
  <div class="app-container home">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="banner-content">
        <h1>会议室预约管理系统</h1>
        <p>高效便捷的会议室预约平台，让会议安排更简单</p>
        <div class="quick-actions">
          <el-button type="primary" size="large" icon="Plus" @click="goToMyReservations">
            快速预约
          </el-button>
          <el-button type="success" size="large" icon="Search" @click="goToRoomList">
            查看会议室
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要功能区域 -->
    <el-row :gutter="20" class="main-content">
      <!-- 我的预约 -->
      <!-- <el-col :xs="24" :sm="24" :md="12" :lg="8"> -->
      <el-col :xs="12" :sm="12" :md="12" :lg="12">
        <el-card class="feature-card">
          <template #header>
            <div class="card-header">
              <span><el-icon><User /></el-icon> 我的预约</span>
              <el-button type="text" @click="goToMyReservations">查看全部</el-button>
            </div>
          </template>
          <div class="my-reservations">
            <div v-if="myReservations.length === 0" class="empty-state">
              <el-icon><DocumentRemove /></el-icon>
              <p>暂无预约记录</p>
            </div>
            <div v-else>
              <div
                v-for="reservation in myReservations"
                :key="reservation.reservationId"
                class="reservation-item"
              >
                <div class="reservation-info">
                  <div class="meeting-title">{{ reservation.meetingTitle }}</div>
                  <div class="meeting-details">
                    <span class="room-name">{{ getRoomName(reservation.roomId) }}</span>
                    <span class="meeting-time">
                      {{ formatDateTime(reservation.startTime) }} - {{ formatDateTime(reservation.endTime) }}
                    </span>
                  </div>
                </div>
                <div class="reservation-status">
                  <el-tag :type="getStatusType(reservation.status)">
                    {{ getStatusText(reservation.status) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 热门会议室 -->
      <el-col :xs="12" :sm="12" :md="12" :lg="12">
        <el-card class="feature-card">
          <template #header>
            <div class="card-header">
              <span><el-icon><Star /></el-icon> 热门会议室</span>
              <el-button type="text" @click="goToRoomList">查看全部</el-button>
            </div>
          </template>
          <div class="popular-rooms">
            <div v-if="popularRooms.length === 0" class="empty-state">
              <el-icon><OfficeBuilding /></el-icon>
              <p>暂无会议室数据</p>
            </div>
            <div v-else>
              <div
                v-for="room in popularRooms.slice(0, 3)"
                :key="room.roomId"
                class="room-item"
                @click="handleRoomReservation(room)"
              >
                <div class="room-image">
                  <img v-if="getImageUrl(room.imageUrl)" :src="getImageUrl(room.imageUrl)" :alt="room.roomName" />
                  <div v-else class="room-placeholder">
                    <el-icon><OfficeBuilding /></el-icon>
                  </div>
                </div>
                <div class="room-info">
                  <div class="room-name">{{ room.roomName }}</div>
                  <div class="room-capacity">
                    <el-icon><User /></el-icon>
                    容纳 {{ room.capacity }} 人
                  </div>
                </div>
                <div class="room-status">
                  <el-tag :type="getRoomStatusType(room.status)" size="small">
                    {{ getRoomStatusText(room.status) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 会议室状态显示屏 -->
    <div class="meeting-room-status-screen">
      <div class="status-screen-header">
        <h2>会议室状态显示屏</h2>
        <div class="current-time">{{ currentTime }}</div>
      </div>

      <el-row :gutter="20" class="room-status-grid">
        <el-col
          v-for="room in roomStatusList"
          :key="room.roomId"
          :xs="12"
          :sm="12"
          :md="12"
          :lg="12"
        >
          <div
            class="room-status-card"
            :class="getRoomStatusClass(room.currentStatus)"
          >
            <div class="room-header">
              <h3>{{ room.roomName }} ({{ room.capacity }}人)</h3>
              <div class="status-indicator">
                <span
                  class="status-badge"
                  :class="getRoomStatusClass(room.currentStatus)"
                >
                  {{ getRoomCurrentStatusText(room.currentStatus) }}
                </span>
              </div>
            </div>

            <div class="room-content">
              <div v-if="room.currentStatus === 'occupied'" class="current-meeting">
                <div class="meeting-info">
                  <div class="meeting-title">{{ room.currentMeeting?.meetingTitle || '会议进行中' }}</div>
                  <div class="meeting-time">
                    {{ formatTime(room.currentMeeting?.startTime) }} - {{ formatTime(room.currentMeeting?.endTime) }}
                  </div>
                  <div class="meeting-organizer">
                    预约人: {{ room.currentMeeting?.userName || '未知' }}
                  </div>
                </div>
                <div class="progress-info">
                  <div class="progress-text">进度: {{ room.progress }}%</div>
                  <el-progress
                    :percentage="room.progress"
                    :stroke-width="6"
                    :show-text="false"
                    color="#409eff"
                  />
                </div>
              </div>

              <div v-else-if="room.currentStatus === 'available'" class="available-info">
                <div class="next-meeting" v-if="room.nextMeeting">
                  <div class="next-label">下次会议</div>
                  <div class="next-title">{{ room.nextMeeting.meetingTitle }}</div>
                  <div class="next-time">
                    {{ formatTime(room.nextMeeting.startTime) }} - {{ formatTime(room.nextMeeting.endTime) }}
                  </div>
                </div>
                <div v-else class="no-meeting">
                  <el-icon><CircleCheck /></el-icon>
                  <span>今日无会议</span>
                </div>
              </div>

              <div v-else class="unavailable-info">
                <el-icon><Lock /></el-icon>
                <span>会议室不可用</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup name="Index">
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  User,
  Star,
  DocumentRemove,
  OfficeBuilding,
  CircleCheck,
  Lock,
  Plus,
  Search
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import {
  listReservation,
  myReservationList
} from '@/api/reserve/reservation'
import { listRoom } from '@/api/search/room'
import useUserStore from '@/store/modules/user'

const router = useRouter()

// 响应式数据
const stats = ref({
  totalRooms: 0,
  availableRooms: 0,
  pendingReservations: 0,
  todayReservations: 0
})

const myReservations = ref([])
const popularRooms = ref([])
const todaySchedule = ref([])
const availableRooms = ref([])
const selectedDate = ref(new Date())
const loading = ref(false)

// 会议室状态显示屏相关数据
const roomStatusList = ref([])
const currentTime = ref('')
const statusUpdateTimer = ref(null)

// 计算属性
const roomMap = computed(() => {
  const map = {}
  popularRooms.value.forEach(room => {
    map[room.roomId] = room
  })
  return map
})

// 生命周期
onMounted(() => {
  loadDashboardData()
  initStatusScreen()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (statusUpdateTimer.value) {
    clearInterval(statusUpdateTimer.value)
  }
})

// 方法
async function loadDashboardData() {
  loading.value = true
  try {
    await Promise.all([
      loadStats(),
      loadMyReservations(),
      loadPopularRooms(),
      loadTodaySchedule(),
      loadAvailableRooms()
    ])
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 初始化状态显示屏
function initStatusScreen() {
  updateCurrentTime()
  loadRoomStatus()

  // 设置定时器，每30秒更新一次状态
  statusUpdateTimer.value = setInterval(() => {
    updateCurrentTime()
    loadRoomStatus()
  }, 30000)

  // 每秒更新时间显示
  setInterval(updateCurrentTime, 1000)
}

// 更新当前时间
function updateCurrentTime() {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const weekDay = ['日', '一', '二', '三', '四', '五', '六'][now.getDay()]
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')

  currentTime.value = `${year}年${month}月${day}日星期${weekDay} ${hours}:${minutes}:${seconds}`
}

// 加载会议室状态
async function loadRoomStatus() {
  try {
    // 获取所有启用的会议室
    const roomResponse = await listRoom({
      pageNum: 1,
      pageSize: 100,
      status: '1'
    })
    const rooms = roomResponse.rows || []

    // 获取今日所有预约
    const today = new Date()
    const todayStr = formatDate(today)
    const reservationResponse = await listReservation({
      pageNum: 1,
      pageSize: 1000,
      startTime: todayStr + ' 00:00:00',
      endTime: todayStr + ' 23:59:59',
      status: '2' // 已通过的预约
    })
    const reservations = reservationResponse.rows || []

    // 处理每个会议室的状态
    roomStatusList.value = rooms.map(room => {
      const roomReservations = reservations.filter(r => r.roomId === room.roomId)
      const now = new Date()

      // 查找当前正在进行的会议
      const currentMeeting = roomReservations.find(r => {
        const startTime = new Date(r.startTime)
        const endTime = new Date(r.endTime)
        return now >= startTime && now <= endTime
      })

      // 查找下一个会议
      const nextMeeting = roomReservations
        .filter(r => new Date(r.startTime) > now)
        .sort((a, b) => new Date(a.startTime) - new Date(b.startTime))[0]

      let currentStatus = 'available'
      let progress = 0

      if (currentMeeting) {
        currentStatus = 'occupied'
        const startTime = new Date(currentMeeting.startTime)
        const endTime = new Date(currentMeeting.endTime)
        const totalDuration = endTime - startTime
        const elapsed = now - startTime
        progress = Math.min(Math.round((elapsed / totalDuration) * 100), 100)
      } else if (room.status !== '1') {
        currentStatus = 'unavailable'
      }

      return {
        ...room,
        currentStatus,
        currentMeeting,
        nextMeeting,
        progress
      }
    })
  } catch (error) {
    console.error('加载会议室状态失败:', error)
  }
}

async function loadStats() {
  try {
    // 获取会议室统计
    const roomResponse = await listRoom({ pageNum: 1, pageSize: 1000 })
    const rooms = roomResponse.rows || []
    stats.value.totalRooms = rooms.length
    stats.value.availableRooms = rooms.filter(room => room.status === '1').length

    // 获取预约统计
    const reservationResponse = await listReservation({
      pageNum: 1,
      pageSize: 1000,
      status: '1' // 待审核
    })
    stats.value.pendingReservations = reservationResponse.total || 0

    // 获取今日预约统计
    const today = new Date()
    const todayStr = formatDate(today)
    const todayReservationResponse = await listReservation({
      pageNum: 1,
      pageSize: 1000,
      startTime: todayStr + ' 00:00:00',
      endTime: todayStr + ' 23:59:59'
    })
    stats.value.todayReservations = todayReservationResponse.total || 0
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

async function loadMyReservations() {
  try {
    const response = await myReservationList({
      pageNum: 1,
      pageSize: 5
    })
    myReservations.value = response.rows || []
  } catch (error) {
    console.error('加载我的预约失败:', error)
  }
}

function getImageUrl(path) {
  return `http://localhost:8080${path}`
}

async function loadPopularRooms() {
  try {
    const response = await listRoom({
      pageNum: 1,
      pageSize: 8,
      status: '1' // 只获取启用的会议室
    })
    console.log(response.rows)
    popularRooms.value = response.rows || []
  } catch (error) {
    console.error('加载热门会议室失败:', error)
  }
}

async function loadTodaySchedule() {
  try {
    const dateStr = formatDate(selectedDate.value)
    const response = await listReservation({
      pageNum: 1,
      pageSize: 20,
      startTime: dateStr + ' 00:00:00',
      endTime: dateStr + ' 23:59:59',
      status: '2' // 已通过的预约
    })
    todaySchedule.value = (response.rows || []).sort((a, b) =>
      new Date(a.startTime) - new Date(b.startTime)
    )
  } catch (error) {
    console.error('加载今日安排失败:', error)
  }
}

async function loadAvailableRooms() {
  try {
    const response = await listRoom({
      pageNum: 1,
      pageSize: 100,
      status: '1' // 只获取启用的会议室
    })
    availableRooms.value = response.rows || []
  } catch (error) {
    console.error('加载可用会议室失败:', error)
  }
}

function handleRoomReservation(room) {
  quickForm.roomId = room.roomId
  // handleQuickReservation()
}

// 导航函数
function goToMyReservations() {
  router.push('/reserve/myReservation')
}

function goToRoomList() {
  router.push('/search/room')
}

// 工具函数
function getRoomName(roomId) {
  const room = roomMap.value[roomId]
  return room ? room.roomName : `会议室${roomId}`
}

function getStatusType(status) {
  const statusMap = {
    '0': 'info',    // 已取消
    '1': 'warning', // 待审核
    '2': 'success', // 已通过
    '3': 'danger',  // 已拒绝
    '4': 'success'  // 已完成
  }
  return statusMap[status] || 'info'
}

function getStatusText(status) {
  const statusMap = {
    '0': '已取消',
    '1': '待审核',
    '2': '已通过',
    '3': '已拒绝',
    '4': '已完成'
  }
  return statusMap[status] || '未知'
}

function getRoomStatusType(status) {
  const statusMap = {
    '0': 'danger',  // 停用
    '1': 'success', // 启用
    '2': 'warning'  // 维护中
  }
  return statusMap[status] || 'info'
}

function getRoomStatusText(status) {
  const statusMap = {
    '0': '停用',
    '1': '启用',
    '2': '维护中'
  }
  return statusMap[status] || '未知'
}

function formatDate(date) {
  if (!date) return ''
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

function formatDateTime(dateTime) {
  if (!dateTime) return ''
  const d = new Date(dateTime)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 状态显示屏相关工具函数
function formatTime(dateTime) {
  if (!dateTime) return ''
  const d = new Date(dateTime)
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  return `${hours}:${minutes}`
}

function getRoomStatusClass(status) {
  const statusMap = {
    'available': 'status-available',
    'occupied': 'status-occupied',
    'unavailable': 'status-unavailable'
  }
  return statusMap[status] || 'status-available'
}

function getRoomCurrentStatusText(status) {
  const statusMap = {
    'available': '空闲',
    'occupied': '使用中',
    'unavailable': '不可用'
  }
  return statusMap[status] || '未知'
}
</script>

<style scoped lang="scss">
.home {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

// 欢迎横幅
.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 40px;
  margin-bottom: 24px;
  color: white;
  text-align: center;

  .banner-content {
    h1 {
      font-size: 32px;
      font-weight: 600;
      margin: 0 0 12px 0;
    }

    p {
      font-size: 16px;
      margin: 0 0 32px 0;
      opacity: 0.9;
    }

    .quick-actions {
      .el-button {
        margin: 0 8px;
        padding: 12px 24px;
        font-size: 16px;
      }
    }
  }
}

// 主要功能区域
.main-content {
  margin-bottom: 24px;
}

.feature-card {
  height: 100%;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      display: flex;
      align-items: center;
      font-weight: 600;

      .el-icon {
        margin-right: 8px;
      }
    }
  }
}

// 我的预约
.my-reservations {
  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #909399;

    .el-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    p {
      margin: 0 0 16px 0;
    }
  }

  .reservation-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .reservation-info {
      flex: 1;

      .meeting-title {
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
      }

      .meeting-details {
        font-size: 12px;
        color: #909399;

        .room-name {
          margin-right: 12px;
        }
      }
    }

    .reservation-status {
      flex-shrink: 0;
    }
  }
}

// 热门会议室
.popular-rooms {
  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #909399;

    .el-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    p {
      margin: 0;
    }
  }

  .room-item {
    display: flex;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f8f9fa;
    }

    &:last-child {
      border-bottom: none;
    }

    .room-image {
      width: 48px;
      height: 48px;
      margin-right: 12px;
      border-radius: 4px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .room-placeholder {
        width: 100%;
        height: 100%;
        background-color: #f0f0f0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #c0c4cc;
      }
    }

    .room-info {
      flex: 1;

      .room-name {
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
      }

      .room-capacity {
        font-size: 12px;
        color: #909399;
        display: flex;
        align-items: center;
        margin-bottom: 4px;

        .el-icon {
          margin-right: 4px;
        }
      }
    }
  }
}

// 会议室状态显示屏样式
.meeting-room-status-screen {
  margin-top: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  border-radius: 16px;
  color: white;

  .status-screen-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);

    h2 {
      margin: 0;
      font-size: 28px;
      font-weight: 600;
      color: white;
    }

    .current-time {
      font-size: 18px;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.9);
    }
  }

  .room-status-grid {
    .room-status-card {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 16px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
      }

      &.status-available {
        border-left: 4px solid #67c23a;

        .status-badge {
          background: #67c23a;
          color: white;
        }
      }

      &.status-occupied {
        border-left: 4px solid #e6a23c;

        .status-badge {
          background: #e6a23c;
          color: white;
        }
      }

      &.status-unavailable {
        border-left: 4px solid #f56c6c;

        .status-badge {
          background: #f56c6c;
          color: white;
        }
      }

      .room-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: white;
        }

        .status-indicator {
          .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
          }
        }
      }

      .room-content {
        .current-meeting {
          .meeting-info {
            margin-bottom: 16px;

            .meeting-title {
              font-size: 16px;
              font-weight: 600;
              color: white;
              margin-bottom: 8px;
            }

            .meeting-time {
              font-size: 14px;
              color: rgba(255, 255, 255, 0.8);
              margin-bottom: 4px;
            }

            .meeting-organizer {
              font-size: 12px;
              color: rgba(255, 255, 255, 0.7);
            }
          }

          .progress-info {
            .progress-text {
              font-size: 12px;
              color: rgba(255, 255, 255, 0.8);
              margin-bottom: 8px;
            }
          }
        }

        .available-info {
          .next-meeting {
            .next-label {
              font-size: 12px;
              color: rgba(255, 255, 255, 0.7);
              margin-bottom: 4px;
            }

            .next-title {
              font-size: 14px;
              font-weight: 600;
              color: white;
              margin-bottom: 4px;
            }

            .next-time {
              font-size: 12px;
              color: rgba(255, 255, 255, 0.8);
            }
          }

          .no-meeting {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: rgba(255, 255, 255, 0.8);

            .el-icon {
              margin-right: 8px;
              font-size: 18px;
            }
          }
        }

        .unavailable-info {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 20px;
          color: rgba(255, 255, 255, 0.8);

          .el-icon {
            margin-right: 8px;
            font-size: 18px;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .welcome-banner {
    padding: 24px;

    .banner-content {
      h1 {
        font-size: 24px;
      }

      p {
        font-size: 14px;
      }

      .quick-actions {
        .el-button {
          padding: 8px 16px;
          font-size: 14px;
        }
      }
    }
  }

  .stat-card {
    padding: 16px;

    .stat-icon {
      width: 40px;
      height: 40px;
      font-size: 20px;
      margin-right: 12px;
    }

    .stat-content {
      .stat-number {
        font-size: 20px;
      }
    }
  }

  .operation-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .operation-item {
    padding: 16px;

    .operation-icon {
      width: 40px;
      height: 40px;
      font-size: 18px;
      margin-bottom: 8px;
    }

    .operation-text {
      font-size: 12px;
    }
  }

  .timeline-item {
    .timeline-time {
      width: 80px;
      margin-right: 12px;

      .time-start {
        font-size: 14px;
      }
    }
  }

  // 状态显示屏移动端适配
  .meeting-room-status-screen {
    margin-top: 20px;
    padding: 16px;

    .status-screen-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;

      h2 {
        font-size: 20px;
      }

      .current-time {
        font-size: 14px;
      }
    }

    .room-status-grid {
      .room-status-card {
        padding: 16px;

        .room-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;

          h3 {
            font-size: 16px;
          }
        }

        .room-content {
          .current-meeting {
            .meeting-info {
              .meeting-title {
                font-size: 14px;
              }

              .meeting-time {
                font-size: 12px;
              }

              .meeting-organizer {
                font-size: 11px;
              }
            }
          }

          .available-info {
            .next-meeting {
              .next-title {
                font-size: 12px;
              }

              .next-time {
                font-size: 11px;
              }
            }

            .no-meeting {
              padding: 16px;
              font-size: 12px;
            }
          }

          .unavailable-info {
            padding: 16px;
            font-size: 12px;
          }
        }
      }
    }
  }
}
</style>